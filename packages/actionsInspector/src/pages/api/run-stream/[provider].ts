import type { NextApiRequest, NextApiResponse } from 'next';
import { runSlackTest } from '../../../tests/slack';
import { runGoogleDriveTest } from '../../../tests/google-drive';

const runners: Record<string, () => Promise<any[]>> = {
  slack: runSlackTest,
  'google-drive': runGoogleDriveTest,
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') return res.status(405).end();
  const { provider } = req.query;
  const runner = typeof provider === 'string' ? runners[provider] : undefined;
  if (!runner) return res.status(404).json({ error: 'unknown provider' });

  // Set up Server-Sent Events
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
  });

  const sendEvent = (type: string, data: any) => {
    res.write(`data: ${JSON.stringify({ type, ...data })}\n\n`);
  };

  try {
    sendEvent('start', {});

    // Mock implementation for now - we'll replace this with real streaming from providerRunner
    const results = await runner();

    for (const result of results) {
      sendEvent('progress', {
        step: {
          action: result.action,
          input: result.input,
          output: result.output,
          valid: result.valid,
          error: result.error
        }
      });

      // Add a small delay to simulate streaming
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    sendEvent('complete', { results });
  } catch (err: any) {
    sendEvent('error', { error: err.message });
  } finally {
    res.end();
  }
}
